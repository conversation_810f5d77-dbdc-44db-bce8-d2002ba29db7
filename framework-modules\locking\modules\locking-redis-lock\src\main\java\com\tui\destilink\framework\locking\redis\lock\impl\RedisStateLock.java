package com.tui.destilink.framework.locking.redis.lock.impl;

import com.tui.destilink.framework.locking.redis.lock.config.RedisLockProperties;
import com.tui.destilink.framework.locking.redis.lock.exception.LockAcquisitionException;
import com.tui.destilink.framework.locking.redis.lock.exception.LockReleaseException;
import com.tui.destilink.framework.locking.redis.lock.AbstractRedisLock;
import com.tui.destilink.framework.locking.redis.lock.service.LockOwnerSupplier;
import com.tui.destilink.framework.locking.redis.lock.service.LockWatchdog;
import com.tui.destilink.framework.locking.redis.lock.service.RedisLockOperations;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;

/**
 * Redis-based implementation of a state lock.
 * <p>
 * This lock type allows acquiring a lock only if an associated state key
 * holds a specific expected value. It also supports updating the state
 * upon lock acquisition or release.
 * </p>
 * <p>
 * The state is stored in a separate Redis String key, and operations
 * are atomic via Lua scripts.
 * </p>
 */
@Slf4j
public class RedisStateLock extends AbstractRedisLock {

    private static final String LOCK_TYPE = "RedisStateLock";
    private static final String STATE_KEY_SUFFIX = ":state";
    private final String stateKey;
    private final String expectedState;
    private final String newStateOnUnlock;
    private final Duration stateExpiration;
    private final boolean initIfNotExist;

    /**
     * Creates a new Redis-based state lock with the specified key and
     * configuration.
     *
     * @param redisLockOperations Redis lock operations for executing lock commands
     * @param lockOwnerSupplier   Supplier for lock owner identifiers
     * @param properties          Redis lock configuration properties
     * @param lockKey             The unique key identifying this lock in Redis
     * @param expectedState       The state value expected for acquisition
     * @param newStateOnUnlock    The state value to set upon unlock
     * @param stateExpiration     Optional TTL for the state key
     * @param initIfNotExist      Whether to initialize the state if it doesn't
     *                            exist
     * @param requestUuid         Unique UUID for the request, used for idempotency
     */
    public RedisStateLock(
            RedisLockOperations redisLockOperations,
            LockOwnerSupplier lockOwnerSupplier,
            RedisLockProperties properties,
            String lockKey,
            String expectedState,
            String newStateOnUnlock,
            Duration stateExpiration,
            boolean initIfNotExist,
            String requestUuid,
            ExecutorService virtualThreadExecutor,
            LockWatchdog watchdog) {
        super(redisLockOperations, lockOwnerSupplier, properties, lockKey, virtualThreadExecutor, watchdog);
        this.expectedState = expectedState;
        this.newStateOnUnlock = newStateOnUnlock;
        this.stateExpiration = stateExpiration;
        this.initIfNotExist = initIfNotExist;
        this.stateKey = lockKey + STATE_KEY_SUFFIX;
    }

    @Override
    protected String getLockType() {
        return LOCK_TYPE;
    }

    @Override
    protected CompletableFuture<Void> doLock(String ownerId, Duration effectiveTimeout) {
        return CompletableFuture.runAsync(() -> {
            long startTime = System.currentTimeMillis();
            long timeoutMillis = effectiveTimeout.toMillis();

            while (System.currentTimeMillis() - startTime < timeoutMillis) {
                try {
                    Boolean acquired = doTryLock(ownerId, effectiveTimeout).get();
                    if (acquired) {
                        return; // CompletableFuture<Void> completes normally
                    }
                    Thread.sleep(properties.getDefaults().getRetryInterval().toMillis());
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    throw new LockAcquisitionException(lockKey, getLockType(), ownerId, "Lock acquisition interrupted", e);
                } catch (ExecutionException e) {
                    throw new LockAcquisitionException(lockKey, getLockType(), ownerId, "Lock acquisition failed", e);
                }
            }
            throw new LockAcquisitionException(lockKey, getLockType(), ownerId, "Lock acquisition timed out");
        });
    }

    @Override
    protected CompletableFuture<Boolean> doTryLock(String ownerId, Duration effectiveTimeout) {
        // Use try_state_lock.lua
        String stateExpirationMs = stateExpiration != null ? String.valueOf(stateExpiration.toMillis()) : "nil";
        String initIfNotExistStr = String.valueOf(initIfNotExist);

        return redisLockOperations.tryStateLock(
                lockKey,
                getResponseCacheKey(),
                null, // Let RedisLockOperationsImpl generate UUID centrally
                String.valueOf(effectiveTimeout.toMillis()),
                ownerId,
                expectedState,
                initIfNotExist,
                expectedState, // Use expectedState as initialState if initializing
                null) // Let RedisLockOperationsImpl use configured response cache TTL
                .thenApply(result -> {
                    if (result == null) {
                        log.debug("State lock acquired: lockKey={}, ownerId={}, expectedState={}",
                                lockKey, ownerId, expectedState);
                        return true;
                    } else if ("-1".equals(result)) {
                        log.warn(
                                "State lock acquisition failed due to state mismatch: lockKey={}, ownerId={}, expectedState={}",
                                lockKey, ownerId, expectedState);
                        throw new LockAcquisitionException(
                                lockKey,
                                getLockType(),
                                ownerId,
                                "State Mismatch",
                                "Failed to acquire lock due to state mismatch.");
                    } else if ("-3".equals(result)) {
                        log.warn(
                                "State lock acquisition failed because state not found and not initialized: lockKey={}, ownerId={}, expectedState={}",
                                lockKey, ownerId, expectedState);
                        throw new LockAcquisitionException(
                                lockKey,
                                getLockType(),
                                ownerId,
                                "State Not Found",
                                "Failed to acquire lock because state key was not found and initIfNotExist is false.");
                    } else {
                        log.debug(
                                "State lock not acquired, held by another or expired: lockKey={}, ownerId={}, pttl={}",
                                lockKey, ownerId, result);
                        return false;
                    }
                });
    }

    @Override
    protected CompletableFuture<Void> doUnlock(String ownerId) {
        // Use unlock_state_lock.lua
        String stateExpirationMs = stateExpiration != null ? String.valueOf(stateExpiration.toMillis()) : "nil";

        return redisLockOperations.unlockStateLock(
                lockKey,
                lockKey.replace(":__locks__:", ":__unlock_channels__:"), // Base channel name for Pub/Sub
                getResponseCacheKey(),
                null, // Let RedisLockOperationsImpl generate UUID centrally
                ownerId,
                newStateOnUnlock,
                STATE_KEY_SUFFIX,
                stateExpirationMs,
                null) // Let RedisLockOperationsImpl use configured response cache TTL
                .thenRun(() -> {
                    log.debug("State lock fully released and state updated: lockKey={}, ownerId={}", lockKey,
                            ownerId);
                })
                .exceptionally(ex -> {
                    log.warn(
                            "State lock release failed: lockKey={}, ownerId={}. Lock not held by current owner or already expired.",
                            lockKey, ownerId);
                    throw new LockReleaseException(
                            lockKey,
                            getLockType(),
                            ownerId,
                            "Failed to release state lock - lock not owned by current owner or already expired",
                            ex);
                });
    }

    // This method is specific to RedisStateLock and not part of AbstractRedisLock
    protected CompletableFuture<Boolean> doIsLocked(String ownerId) {
        // This will check if the main lock key exists and is owned by the ownerId
        return redisLockOperations.checkLock(lockKey, ownerId);
    }

    /**
     * Asynchronously retrieves the current state associated with this lock.
     *
     * @return a CompletableFuture that completes with the current state string,
     *         or null if the state key does not exist.
     */
    public CompletableFuture<String> getStateAsync() {
        return redisLockOperations.getString(stateKey);
    }

    /**
     * Asynchronously updates the state associated with this lock.
     * This operation will only succeed if the current thread holds the lock.
     *
     * @param newState The new state value to set.
     * @return a CompletableFuture that completes with true if the state was
     *         updated,
     *         false otherwise (e.g., lock not held by current owner).
     */
    public CompletableFuture<Boolean> updateStateAsync(String newState) {
        String stateExpirationMs = stateExpiration != null ? String.valueOf(stateExpiration.toMillis()) : "nil";
        return redisLockOperations.updateState(
                lockKey,
                getResponseCacheKey(),
                null, // Let RedisLockOperationsImpl generate UUID centrally
                lockOwnerSupplier.get(),
                newState,
                STATE_KEY_SUFFIX,
                stateExpirationMs,
                null) // Let RedisLockOperationsImpl use configured response cache TTL
                .thenApply(result -> result == 1); // Convert Integer to Boolean (1 = success, 0 = failure)
    }

    /**
     * Asynchronously updates the state associated with this lock, but only if the
     * current state matches the expected value.
     * This operation will only succeed if the current thread holds the lock.
     *
     * @param expectedState The state value that must match the current state.
     * @param newState      The new state value to set.
     * @return a CompletableFuture that completes with true if the state was
     *         updated,
     *         false otherwise (e.g., lock not held by current owner or state
     *         mismatch).
     */
    public CompletableFuture<Boolean> updateStateIfEqualsAsync(String expectedState, String newState) {
        String stateExpirationMs = stateExpiration != null ? String.valueOf(stateExpiration.toMillis()) : "nil";
        String ownerId = lockOwnerSupplier.get();

        // Use update_state_if_equals.lua script for atomic operation
        return redisLockOperations.updateStateIfEquals(
                lockKey,
                getResponseCacheKey(),
                null, // Let RedisLockOperationsImpl generate UUID centrally
                ownerId,
                expectedState,
                newState,
                STATE_KEY_SUFFIX,
                stateExpirationMs,
                null) // Let RedisLockOperationsImpl use configured response cache TTL
                .thenApply(result -> {
                    if (result == 1) {
                        log.debug("Successfully updated state atomically: lockKey={}, ownerId={}, expected={}, new={}",
                                lockKey, ownerId, expectedState, newState);
                        return true;
                    } else if (result == 0) {
                        log.debug("Failed to update state - lock not held by current owner: lockKey={}, ownerId={}",
                                lockKey, ownerId);
                        return false;
                    } else if (result == -1) {
                        log.debug(
                                "Failed to update state - state mismatch: lockKey={}, ownerId={}, expected={}, actual=not-matching",
                                lockKey, ownerId, expectedState);
                        return false;
                    } else {
                        log.warn("Unexpected result from updateStateIfEquals: {}", result);
                        return false;
                    }
                });
    }

    @Override
    public CompletableFuture<Void> lockInterruptiblyAsync() {
        log.debug("Attempting to acquire state lock interruptibly: lockKey={}", lockKey);

        // Check for thread interruption before attempting to acquire the lock
        if (Thread.interrupted()) {
            Thread.currentThread().interrupt(); // Restore the interrupted status
            throw new LockAcquisitionException(
                    lockKey,
                    getLockType(),
                    lockOwnerSupplier.get(),
                    "Thread already interrupted");
        }

        return lockAsync().exceptionally(ex -> {
            if (ex instanceof InterruptedException
                    || (ex instanceof CompletionException && ex.getCause() instanceof InterruptedException)) {
                Thread.currentThread().interrupt();
                throw new LockAcquisitionException(lockKey, getLockType(), lockOwnerSupplier.get(),
                        "Lock acquisition interrupted", ex);
            }
            throw new LockAcquisitionException(lockKey, getLockType(), lockOwnerSupplier.get(),
                    "Failed to acquire lock interruptibly", ex);
        });
    }

    @Override
    public CompletableFuture<Boolean> isLockedAsync() {
        return doIsLocked(lockOwnerSupplier.get())
                .thenApply(isLocked -> {
                    log.debug("Checked if state lock is held: lockKey={}, isLocked={}", lockKey, isLocked);
                    return isLocked;
                });
    }

    @Override
    protected CompletableFuture<Void> updateLockState(String ownerId) {
        // For RedisStateLock, updating the lock state means ensuring the state TTL
        // is aligned with the main lock TTL
        String stateExpirationMs = stateExpiration != null ? String.valueOf(stateExpiration.toMillis())
                : String.valueOf(getLockTtlMillis());

        // Get current state first
        return getStateAsync()
                .thenCompose(currentState -> {
                    if (currentState == null) {
                        log.debug("No state found to update for lock: lockKey={}, ownerId={}", lockKey, ownerId);
                        return CompletableFuture.completedFuture(null);
                    }

                    // Update the state with the same value but refresh TTL
                    return redisLockOperations.updateState(
                            lockKey,
                            getResponseCacheKey(),
                            null, // Let RedisLockOperationsImpl generate UUID centrally
                            ownerId,
                            currentState, // Keep the same state value
                            STATE_KEY_SUFFIX,
                            stateExpirationMs, // Align with lock TTL
                            null) // Let RedisLockOperationsImpl use configured response cache TTL
                            .thenAccept(result -> {
                                if (result == 1) {
                                    log.debug("Successfully updated state TTL for lock: lockKey={}, ownerId={}",
                                            lockKey, ownerId);
                                } else {
                                    log.warn("Failed to update state TTL for lock: lockKey={}, ownerId={}", lockKey,
                                            ownerId);
                                }
                            });
                });
    }
}