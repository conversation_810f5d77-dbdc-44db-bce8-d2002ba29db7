package com.tui.destilink.framework.locking.redis.lock.impl;

import com.tui.destilink.framework.locking.redis.lock.AbstractRedisLock;
import com.tui.destilink.framework.locking.redis.lock.config.RedisLockProperties;
import com.tui.destilink.framework.locking.redis.lock.exception.LockAcquisitionException;
import com.tui.destilink.framework.locking.redis.lock.exception.LockReleaseException;
import com.tui.destilink.framework.locking.redis.lock.service.LockOwnerSupplier;
import com.tui.destilink.framework.locking.redis.lock.service.LockWatchdog;
import com.tui.destilink.framework.locking.redis.lock.service.RedisLockOperations;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;

/**
 * Implementation of a Redis-based write lock.
 * This class extends AbstractRedisLock to provide write-specific locking
 * functionality.
 */
@Slf4j
public class RedisWriteLock extends AbstractRedisLock {

    public static final String LOCK_MODE = "WRITE";
    public static final String WRITE_LOCK_TYPE = "RedisWriteLock";

    public RedisWriteLock(
            RedisLockOperations redisLockOperations,
            LockOwnerSupplier lockOwnerSupplier,
            RedisLockProperties properties,
            String lockKey,
            long lockTtlMillis,
            long retryIntervalMillis,
            int maxRetries,
            ExecutorService virtualThreadExecutor,
            LockWatchdog watchdog) {
        super(redisLockOperations, lockOwnerSupplier, properties, lockKey, lockTtlMillis, retryIntervalMillis,
                maxRetries, virtualThreadExecutor, watchdog);
        // No additional initialization needed
        log.debug("Created RedisWriteLock for key: {}", lockKey);
    }

    @Override
    protected CompletableFuture<Void> doLock(String ownerId, Duration effectiveTimeout) {
        log.debug("Attempting to acquire write lock: lockKey={}, ownerId={}, timeout={}", getLockKey(), ownerId,
                effectiveTimeout);
        return redisLockOperations.tryWriteLock(getLockKey(), getResponseCacheKey(), null, // UUID generated internally
                String.valueOf(getLockTtlMillis()), ownerId,
                String.valueOf(getResponseCacheTtlSeconds().toMillis()), LOCK_MODE)
                .thenCompose(acquired -> {
                    if (acquired != null && acquired > 0) {
                        log.debug("Write lock acquired: lockKey={}, ownerId={}", getLockKey(), ownerId);
                        return updateLockState(ownerId);
                    } else {
                        log.debug("Failed to acquire write lock: lockKey={}, ownerId={}", getLockKey(), ownerId);
                        return CompletableFuture.<Void>failedFuture(
                                new LockAcquisitionException(getLockKey(), getLockType(), ownerId,
                                        "Failed to acquire write lock"));
                    }
                });
    }

    @Override
    protected CompletableFuture<Boolean> doTryLock(String ownerId, Duration effectiveTimeout) {
        log.debug("Attempting to try-acquire write lock: lockKey={}, ownerId={}, timeout={}", getLockKey(), ownerId,
                effectiveTimeout);
        return redisLockOperations.tryWriteLock(getLockKey(), getResponseCacheKey(),
                null, String.valueOf(getLockTtlMillis()), ownerId, // UUID generated internally
                String.valueOf(getResponseCacheTtlSeconds().toMillis()), LOCK_MODE)
                .thenApply(acquired -> {
                    boolean success = acquired != null && acquired > 0;
                    if (success) {
                        log.debug("Write lock try-acquired: lockKey={}, ownerId={}", getLockKey(), ownerId);
                        updateLockState(ownerId);
                    } else {
                        log.debug("Failed to try-acquire write lock: lockKey={}, ownerId={}", getLockKey(), ownerId);
                    }
                    return success;
                });
    }

    @Override
    protected CompletableFuture<Void> doUnlock(String ownerId) {
        log.debug("Attempting to release write lock: lockKey={}, ownerId={}", getLockKey(), ownerId);
        return redisLockOperations.unlockWriteLock(getLockKey(), ownerId, null, "UNLOCK") // UUID generated internally
                .thenAccept(result -> {
                    if ("OK".equals(result)) {
                        log.debug("Write lock released: lockKey={}, ownerId={}", getLockKey(), ownerId);
                    } else {
                        log.warn("Failed to release write lock: lockKey={}, ownerId={}, result={}", getLockKey(),
                                ownerId, result);
                        throw new LockReleaseException(getLockKey(), getLockType(), ownerId,
                                "Failed to release write lock");
                    }
                });
    }

    @Override
    public boolean isReadLock() {
        return false;
    }

    @Override
    public boolean isWriteLock() {
        return true;
    }

    @Override
    protected CompletableFuture<Void> updateLockState(String ownerId) {
        return CompletableFuture.allOf(
                redisLockOperations.hset(getLockKey(), "mode", LOCK_MODE).thenApply(result -> null),
                redisLockOperations.hset(getLockKey(), "owner", ownerId).thenApply(result -> null));
    }

    @Override
    public String getLockType() {
        return WRITE_LOCK_TYPE;
    }
}